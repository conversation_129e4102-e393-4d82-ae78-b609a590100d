"""
Enhanced prompt builder for test script generation.

This module provides a modularized version of the generate_test_script_prompt function
with improved prompt quality, better error handling, and comprehensive logging.

Usage:
    from core.prompt_builder import generate_test_script_prompt

    prompt = generate_test_script_prompt(
        test_case, step_matches, test_data, website_url,
        step_table_entry, state
    )

Features:
1. Enhanced prompt quality with clear variable naming conventions
2. Robust locator generation and element handling guidelines
3. Comprehensive error handling with defensive programming
4. Detailed logging for debugging and troubleshooting
5. Backward compatibility with existing AI integration
"""

from __future__ import annotations

import json
import logging
import traceback
import sys
import inspect
from typing import Any, Dict, List, Optional
import os
import time

logger = logging.getLogger("ScriptWeaver.prompt_builder")

# Helper utilities
def safe_int(val: Any, default: Optional[int] = None) -> Optional[int]:
    """Parse val to int or return default if that fails."""
    try:
        return int(val)
    except (TypeError, ValueError):
        return default


def get_by_pos(items: List[Any], pos: int, default: Any = None) -> Any:
    """Safely get item at position from list."""
    try:
        return items[pos] if 0 <= pos < len(items) else default
    except (IndexError, TypeError):
        return default


def load_json_step_data(test_case_id: str) -> Optional[List[Dict[str, Any]]]:
    """Load step data from JSON storage for the given test case ID."""
    try:
        from core.step_data_storage import get_step_data_storage
        storage = get_step_data_storage()
        result = storage.load_step_data(test_case_id)

        if result:
            step_data, metadata = result
            logger.info(f"Loaded {len(step_data)} steps from JSON storage for test case {test_case_id}")
            return step_data
        else:
            logger.warning(f"No JSON step data found for test case {test_case_id}")
            return None

    except Exception as e:
        logger.error(f"Error loading JSON step data for test case {test_case_id}: {e}")
        return None


def pick_current_step_from_json(test_case_id: str, step_table_entry: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """Resolve the current test step from JSON storage exclusively."""
    if step_table_entry is not None:
        # Use the step_table_entry as the authoritative source
        logger.info(f"Using step_table_entry as current step for test case {test_case_id}")
        return step_table_entry

    # Fallback: load from JSON and get first step
    json_steps = load_json_step_data(test_case_id)
    if json_steps and len(json_steps) > 0:
        logger.info(f"Using first step from JSON storage for test case {test_case_id}")
        return json_steps[0]

    raise ValueError(f"No step data available for test case {test_case_id}. Cannot generate script.")


def build_previous_steps_info_from_json(previous_step_numbers: List[str], test_case_id: str) -> str:
    """Build information about previously implemented steps from JSON storage."""
    if not previous_step_numbers:
        return ""

    info = "Previous steps that have been implemented:\n"

    # Load step data from JSON storage
    json_steps = load_json_step_data(test_case_id)
    if not json_steps:
        return info + "(Error: No JSON step data available)\n"

    # Create a lookup dictionary for faster access
    steps_by_no = {str(step.get('step_no', '')): step for step in json_steps}

    for step_no in previous_step_numbers:
        try:
            step_no_str = str(step_no) if step_no is not None else ""

            if step_no_str in steps_by_no:
                step = steps_by_no[step_no_str]
                info += f"\nStep {step_no}:\n"
                info += f"Action: {step.get('action', 'N/A')}\n"
                info += f"Expected: {step.get('expected_result', 'N/A')}\n"
                info += f"Description: {step.get('step_description', 'N/A')}\n"

                # Include additional context if available
                if step.get('locator'):
                    info += f"Locator: {step.get('locator_strategy', '')} = {step.get('locator')}\n"
                if step.get('test_data_param'):
                    info += f"Test Data: {step.get('test_data_param')}\n"
            else:
                info += f"\nStep {step_no}: (Step details not found in JSON storage)\n"

        except Exception as e:
            logger.warning(f"Error processing step {step_no}: {type(e).__name__}: {str(e)}")
            info += f"\nStep {step_no}: (Error processing step details)\n"

    return info


def build_previous_context(state: Any) -> str:
    """Build context information from previous steps."""
    if not state:
        return ""

    context = "\nContext from previous steps:\n"

    # Browser initialization info
    try:
        if hasattr(state, 'browser_initialized') and state.browser_initialized:
            context += "- Browser has already been initialized\n"
    except Exception as e:
        logger.warning(f"Error checking browser_initialized: {e}")

    # Imports
    try:
        if hasattr(state, 'script_imports') and state.script_imports:
            if isinstance(state.script_imports, (list, tuple, set)):
                context += "- Imports from previous steps:\n"
                for imp in state.script_imports:
                    context += f"  {imp}\n"
    except Exception as e:
        logger.warning(f"Error processing script_imports: {e}")

    # Fixtures
    try:
        if hasattr(state, 'script_fixtures') and state.script_fixtures:
            if isinstance(state.script_fixtures, (list, tuple, set)):
                context += "- Fixtures from previous steps:\n"
                for fixture in state.script_fixtures:
                    try:
                        fixture_parts = fixture.split()
                        fixture_name = fixture_parts[1] if len(fixture_parts) > 1 else fixture
                        context += f"  {fixture_name}\n"
                    except Exception:
                        context += f"  {fixture}\n"
    except Exception as e:
        logger.warning(f"Error processing script_fixtures: {e}")

    # Functions
    try:
        if hasattr(state, 'script_functions') and state.script_functions:
            if isinstance(state.script_functions, dict):
                context += "- Helper functions from previous steps:\n"
                for func_name in state.script_functions.keys():
                    context += f"  {func_name}\n"
    except Exception as e:
        logger.warning(f"Error processing script_functions: {e}")

    # Variables
    try:
        if hasattr(state, 'script_variables') and state.script_variables:
            if isinstance(state.script_variables, dict):
                context += "- Shared variables from previous steps:\n"
                for var_name, var_value in state.script_variables.items():
                    context += f"  {var_name} = {var_value}\n"
    except Exception as e:
        logger.warning(f"Error processing script_variables: {e}")

    return context


def get_previous_step_numbers_from_json(current_step: Dict[str, Any], state: Any) -> List[str]:
    """Get list of previous step numbers that have been implemented, using JSON step data."""
    if not (state and hasattr(state, 'previous_scripts') and state.previous_scripts):
        return []

    try:
        # Get current step number from JSON step data (not original test case)
        current_step_no = str(current_step.get('step_no', '0'))
        if not current_step_no or current_step_no.strip() == '':
            current_step_no = '0'

        logger.debug(f"Current step number from JSON: {current_step_no}")

        if not isinstance(state.previous_scripts, dict):
            logger.warning(f"state.previous_scripts is not a dictionary: {type(state.previous_scripts)}")
            return []

        previous_step_numbers = []
        for step_no in list(state.previous_scripts.keys()):
            try:
                step_no_int = safe_int(step_no)
                current_step_no_int = safe_int(current_step_no)

                logger.debug(f"Comparing step numbers: step_no={step_no}→{step_no_int}, current_step_no={current_step_no}→{current_step_no_int}")

                if step_no_int is not None and current_step_no_int is not None and step_no_int < current_step_no_int:
                    previous_step_numbers.append(step_no)
            except Exception as e:
                logger.warning(f"Error processing step number: {step_no}, error: {str(e)}")
                continue

        # Sort previous steps
        try:
            def safe_sort_key(x):
                val = safe_int(x, -1)
                return val if val is not None else -1

            previous_step_numbers.sort(key=safe_sort_key)
            logger.info(f"Found {len(previous_step_numbers)} previous steps with scripts: {', '.join(previous_step_numbers)}")
        except Exception as e:
            logger.warning(f"Error sorting previous step numbers: {str(e)}")

        return previous_step_numbers

    except Exception as e:
        logger.error(f"Error processing previous scripts: {e}")
        return []


# Main entry point
def generate_test_script_prompt(
    test_case: Dict[str, Any],
    step_matches: Dict[str, Any],
    test_data: Dict[str, Any],
    website_url: str,
    step_table_entry: Optional[Dict[str, Any]] = None,
    state: Optional[Any] = None,
) -> str:
    """
    Generate a comprehensive prompt for test script generation using JSON step data as the single source of truth.

    This function now sources all step information exclusively from JSON step data storage,
    ensuring that manual edits, hybrid editing changes, and AI-generated step descriptions
    are the authoritative source for script generation context.

    Args:
        test_case: The test case to generate a script for (used only for ID and metadata)
        step_matches: Dictionary of element matches for test steps
        test_data: Dictionary of test data values
        website_url: The URL of the website to test
        step_table_entry: The step table entry for the step (required for JSON-based approach)
        state: The application state manager instance for script continuity (optional)

    Returns:
        str: The enhanced prompt for test script generation

    Raises:
        TypeError: If test_case is not a dictionary
        ValueError: If no step data is available from JSON storage
    """
    try:
        # Validate inputs
        if not isinstance(test_case, dict):
            raise TypeError("test_case must be a dict")

        # Get test case ID for JSON storage lookup
        test_case_id = test_case.get('Test Case ID')
        if not test_case_id:
            raise ValueError("Test case must have a 'Test Case ID' field for JSON storage lookup.")

        # Validate that we have step_table_entry (required for JSON-based approach)
        if not step_table_entry:
            raise ValueError("step_table_entry is required for JSON-based prompt generation.")

        # Add debug log after input validation
        logger.debug(
            "DEBUG prompt (JSON-based): test_case_id=%s, step_no=%s, prev_keys=%s",
            test_case_id,
            step_table_entry.get('step_no') if step_table_entry else 'N/A',
            list(state.previous_scripts.keys()) if (state and hasattr(state,'previous_scripts')) else []
        )

        # Use JSON-based functions exclusively
        current_step = pick_current_step_from_json(test_case_id, step_table_entry)
        previous_step_numbers = get_previous_step_numbers_from_json(current_step, state)
        previous_steps_info = build_previous_steps_info_from_json(previous_step_numbers, test_case_id)
        previous_context = build_previous_context(state)

        # Log that we're using the merge_step_scripts approach
        if previous_steps_info:
            logger.info("Using merge_step_scripts approach for script continuity instead of including full scripts in prompt")

        # Extract step number and create slug for function naming from JSON data
        step_no = current_step.get('step_no', '1')
        action_text = current_step.get('action', 'action')
        slug = (action_text.split() or ['action'])[0].lower()

        # Determine if test_data fixture is needed
        needs_test_data = bool(test_data and any(
            key.startswith('manual_input_for_step_') or
            key in ['username', 'password', 'email', 'input_text']
            for key in test_data.keys()
        ))

        # Build fixture parameters
        fixture_params = "browser"
        if needs_test_data:
            fixture_params += ", test_data"

        # Create a detailed test data section for the prompt
        test_data_instructions = ""
        if needs_test_data and test_data:
            test_data_instructions = f"""

        ## Test Data Values
        Use these EXACT values from the manual input data:
        ```python
        # Access test data values like this:
        {chr(10).join([f'        {key} = test_data["{key}"]  # Value: "{value}"' for key, value in test_data.items() if key.startswith('manual_input_for_step_') or key in ['username', 'password', 'email', 'input_text']])}
        ```

        **IMPORTANT**: Use the actual values shown above, not placeholder values like 'any_user' or 'any_password'.
        The test_data fixture will provide these exact values at runtime.
        """

        # Build the concise prompt template using JSON step data exclusively
        prompt = f"""
        ## Role: You are a senior QA-automation engineer.

        ## Context
        Test Case: {test_case.get('Test Case ID')}
        Objective: {test_case.get('Test Case Objective')}
        {previous_steps_info}
        {previous_context if previous_context else ""}

        ## Step (from JSON storage)
        - No: {step_no}
        - Type: {current_step.get('step_type', 'ui')}
        - Action: {current_step.get('action', 'N/A')}
        - Locator Strategy: {current_step.get('locator_strategy', 'N/A')}
        - Locator: {current_step.get('locator', 'N/A')}
        - Test Data Param: {current_step.get('test_data_param', 'N/A')}
        - Expected Result: {current_step.get('expected_result', 'N/A')}
        - Assertion Type: {current_step.get('assertion_type', 'N/A')}
        - Description: {current_step.get('step_description', 'No description available')}
        - Timeout: {current_step.get('timeout', 10)}s

        ## Data & Elements
        Element matches:
        ```json
        {json.dumps(step_matches, indent=2)}
        ```
        Test data:
        ```json
        {json.dumps(test_data, indent=2)}
        ```
        {test_data_instructions}

        ## Requirements ✅
        1. Produce **only** Python code (no markdown).
        2. Implement *exactly* this step in a test named `test_step{step_no}_{slug}`.
        3. **Function signature** → `def test_step{step_no}_{slug}(browser):`   Do **not** add other parameters.
        4. Access `test_data` test_data = {json.dumps(test_data, indent=4)} **inside** the function.  do NOT add it as a parameter.
        5. Prepend **@pytest.mark.order({step_no})** so PyTest runs the steps in
           • numerical order.  Make sure **import pytest** appears exactly once
           • at the top of the file (add it if it’s missing).
        6. Locate elements via `WebDriverWait(browser, 10)` + suitable EC conditions.
           • For input/text fields: wait for **visibility_of_element_located**,then `field.click(); field.clear(); field.send_keys(value)`.
           • After typing, wait with  `WebDriverWait(browser, 5).until(EC.text_to_be_present_in_element_value(locator, value))` before asserting the field’s `.get_attribute("value")`.
        7. If action is navigation (`navigate|open|visit`):
           – `browser.get("{website_url}")` then assert URL (`EC.url_to_be|contains`).
           – No other element checks unless **Expected** mentions "title".
        8. Insert `time.sleep(random.uniform(0.5, 1.5))` after user actions to mimic pacing.
        9. **Assertion required**: include at least **one** `assert` reflecting *Expected* (e.g., element visible, value equals, URL matches). Waiting alone is **not** sufficient.
        10. Failure handling:
            • Wrap body in try/except (`Exception`).
            • rely on the global screenshot hook provided by conftest.py;
            • **do not** define extra fixtures such as `screenshot_on_failure`.
        11. Access `test_data` **inside the function** (do **not** add it as a parameter or fixture). Use keys verbatim and avoid placeholder literals.
        12. Import/define only what you use – duplicates will be hoisted later.
        13. Do not call browser.quit() or re-initialise WebDriver; the shared fixture handles lifecycle.

        Return the code.
        """

        return prompt.strip()

    except Exception as e:
        # Enhanced error handling with detailed diagnostics
        logger.error(f"Error generating test script prompt: {e}")
        logger.error(f"Error type: {type(e).__name__}")

        # Get traceback information
        tb = sys.exc_info()[2]
        if tb:
            tb_frame = tb.tb_frame
            line_no = tb.tb_lineno

            # Get the source code of the line that caused the error
            try:
                source_lines, starting_line = inspect.getsourcelines(tb_frame)
                error_line = source_lines[line_no - starting_line - 1].strip()
            except Exception:
                error_line = "Could not retrieve source line"

            # Get the full traceback
            tb_str = traceback.format_exc()
            logger.error(f"Traceback:\n{tb_str}")

            # Handle IndexError specifically with enhanced diagnostics
            if isinstance(e, IndexError):
                # Extract variable information from the frame locals
                locals_dict = tb_frame.f_locals

                # Try to identify the list and index that caused the error
                list_vars = {name: value for name, value in locals_dict.items()
                            if isinstance(value, (list, tuple, dict)) and name != 'self'}

                # Build diagnostic information
                diagnostics = []
                for name, value in list_vars.items():
                    if isinstance(value, (list, tuple)):
                        diagnostics.append(f"{name}: type={type(value).__name__}, length={len(value)}")
                    elif isinstance(value, dict):
                        diagnostics.append(f"{name}: type=dict, keys={list(value.keys())}, length={len(value)}")

                # Create detailed error message
                error_msg = (
                    f"IndexError at line {line_no} in generate_test_script_prompt: {str(e)}\n"
                    f"Error occurred in line: {error_line}\n"
                    f"Variable state at time of error:\n"
                    f"{chr(10).join(diagnostics)}\n"
                    f"test_case has Steps: {bool(test_case and test_case.get('Steps'))}\n"
                    f"step_table_entry: {step_table_entry}\n"
                    f"state has previous_scripts: {bool(state and hasattr(state, 'previous_scripts'))}"
                )

                logger.error(error_msg)
                return error_msg
            else:
                # For other exceptions, return a user-friendly error message
                error_msg = (
                    f"Error generating test script prompt: {type(e).__name__} at line {line_no}: {str(e)}\n"
                    f"Error occurred in line: {error_line}"
                )
                logger.error(error_msg)
                return error_msg


def generate_script_validation_prompt(
    script_content: str,
    test_case: Dict[str, Any],
    step_table_entry: Optional[Dict[str, Any]] = None,
    test_data: Optional[Dict[str, Any]] = None,
    element_matches: Optional[Dict[str, Any]] = None
) -> str:
    """
    Generate a concise prompt for validating generated test scripts.

    This function creates a 10-point instruction template for AI validation
    of generated test scripts, focusing on code quality, syntax, and best practices.

    Args:
        script_content: The generated test script to validate
        test_case: The test case information
        step_table_entry: The step table entry for context
        test_data: Test data used in the script
        element_matches: Element matches used in the script

    Returns:
        str: The validation prompt following the 10-point template format
    """
    try:
        # Extract key information for context
        test_case_id = test_case.get('Test Case ID', 'Unknown')
        step_no = step_table_entry.get('step_no', '1') if step_table_entry else '1'
        action = step_table_entry.get('action', '') if step_table_entry else ''
        expected = step_table_entry.get('expected', '') if step_table_entry else ''

        # Check if test_data fixture is used
        uses_test_data = bool(test_data and 'test_data' in script_content)

        # Build the concise validation prompt using 10-point template
        prompt = f"""
## Role: You are a senior QA automation code reviewer.

## Context
Test Case: {test_case_id}
Step: {step_no} - {action}
Expected: {expected}
Uses test_data fixture: {uses_test_data}

## Script to Validate
```python
{script_content}
```

## Instructions
Analyze the script and provide a quality assessment following these 10 points:

1. **Syntax Check**: Verify Python syntax is valid and imports are correct.
2. **WebDriver Usage**: Confirm 'browser' fixture parameter is used (not 'driver').
3. **Locator Strategy**: Check element selection methods are appropriate and realistic.
4. **Wait Conditions**: Verify proper WebDriverWait usage with appropriate timeouts.
5. **Test Data Integration**: Validate manual input data is accessed correctly from test_data fixture.
6. **Action Sequencing**: Ensure actions follow logical order (wait → interact → assert).
7. **Error Handling**: Check try/except blocks are properly implemented.
8. **Assertions**: Verify at least one meaningful assertion exists reflecting Expected Result.
9. **Best Practices**: Confirm pytest conventions, function naming, and code structure.
10. **Integration**: Assess compatibility with conftest.py fixtures and previous steps.

## Response Format
Provide your analysis as JSON:
```json
{{
  "syntax_valid": true,
  "quality_score": 85,
  "issues_found": [
    {{"category": "locator", "severity": "medium", "description": "Generic XPath locator used"}},
    {{"category": "assertion", "severity": "low", "description": "Could add more specific assertion"}}
  ],
  "recommendations": [
    "Use more specific CSS selectors instead of generic XPath",
    "Add explicit wait before assertion"
  ],
  "confidence_rating": "high",
  "ready_for_execution": true,
  "validation_status": "good"
}}
```

**Quality Score Guidelines:**
- **90-100**: Excellent code with best practices, optimal locators, proper waits, comprehensive assertions
- **80-89**: Good code with minor improvements needed (e.g., better locators, additional waits)
- **60-79**: Acceptable code with moderate issues (e.g., generic locators, missing assertions)
- **40-59**: Poor code with significant issues (e.g., no waits, weak assertions, syntax problems)
- **0-39**: Failing code with critical issues (e.g., syntax errors, missing core functionality)

Return only the JSON response.
        """

        return prompt.strip()

    except Exception as e:
        logger.error(f"Error generating script validation prompt: {e}")
        return f"Error generating validation prompt: {str(e)}"


def generate_validation_guidelines(common_issues: List[Dict[str, Any]] = None) -> str:
    """
    Generate validation guidelines based on common issues and best practices.

    Args:
        common_issues: List of common validation issues from feedback history

    Returns:
        str: Formatted validation guidelines for inclusion in prompts
    """
    try:
        # Base validation guidelines (always included)
        base_guidelines = """
## Validation Guidelines (Follow these to ensure high-quality scripts)

### Locator Best Practices:
- **Prefer CSS selectors** over XPath when possible (e.g., `#login-btn` instead of `//button[@id='login-btn']`)
- **Use specific selectors**: Avoid generic locators like `//div[1]` or `button`
- **Prioritize stable attributes**: Use `id`, `name`, `data-testid` over class names
- **Example good locators**: `#username`, `[data-testid="submit-btn"]`, `input[name="password"]`

### Wait Conditions:
- **Always wait before interactions**: Use `WebDriverWait` with appropriate conditions
- **Wait for visibility**: Use `visibility_of_element_located` before clicking/typing
- **Wait for text presence**: Use `text_to_be_present_in_element` after typing
- **Appropriate timeouts**: Use 10s for UI elements, 30s for page loads

### Assertion Requirements:
- **Include meaningful assertions**: Every test must have at least one `assert` statement
- **Assert expected outcomes**: Verify URL changes, element visibility, text content
- **Use specific assertions**: `assert "dashboard" in browser.current_url` not just `assert True`
- **Wait before asserting**: Ensure elements are ready before checking their state

### Error Handling:
- **Wrap test body in try/except**: Catch `Exception` for comprehensive error handling
- **Don't define custom fixtures**: Use existing conftest.py screenshot handling
- **Log meaningful errors**: Include context in error messages

### Test Data Integration:
- **Access test_data inside function**: Don't add as parameter, use `test_data["key"]`
- **Use exact values**: Don't use placeholders like 'any_user', use actual test data
- **Validate data exists**: Check test_data keys before using them
"""

        # Add specific guidelines based on common issues with adaptive frequency threshold
        if common_issues:
            # Calculate adaptive frequency threshold based on feedback volume
            total_feedback_count = sum(issue.get('frequency', 1) for issue in common_issues)
            feedback_entries = len(common_issues)

            # Use lower threshold for early learning, higher for established patterns
            if feedback_entries <= 3:
                frequency_threshold = 1  # Include all issues when learning
                threshold_reason = "early learning phase"
            elif feedback_entries <= 10:
                frequency_threshold = max(1, total_feedback_count // feedback_entries)  # Average frequency
                threshold_reason = "medium learning phase"
            else:
                frequency_threshold = 2  # Standard threshold for established patterns
                threshold_reason = "established patterns phase"

            # Log adaptive threshold calculation
            logger.info(f"Validation guidelines: Adaptive threshold calculation")
            logger.info(f"  - Feedback entries: {feedback_entries}")
            logger.info(f"  - Total frequency count: {total_feedback_count}")
            logger.info(f"  - Calculated threshold: {frequency_threshold} ({threshold_reason})")

            specific_guidelines = f"\n### Specific Improvements (Based on Recent Feedback - Threshold: {frequency_threshold}):\n"
            guidelines_added = 0

            # Sort issues by frequency (descending) and recency
            sorted_issues = sorted(common_issues, key=lambda x: (x.get('frequency', 1), x.get('severity_weight', 1)), reverse=True)

            # Log which guidelines will be included
            logger.info(f"Validation guidelines: Processing {len(sorted_issues)} sorted issues")

            for issue in sorted_issues[:8]:  # Limit to top 8 issues
                issue_type = issue.get('type', 'issue')
                category = issue.get('category', 'general')
                description = issue.get('description', '')
                frequency = issue.get('frequency', 1)
                severity = issue.get('severity', 'medium')

                if frequency >= frequency_threshold:
                    if issue_type == 'recommendation':
                        frequency_indicator = f" ({frequency}x)" if frequency > 1 else ""
                        specific_guidelines += f"- **{category.title()}**: {description}{frequency_indicator}\n"
                        guidelines_added += 1
                        logger.info(f"  [+] Added recommendation: {category} (freq: {frequency})")
                    elif issue_type == 'issue':
                        severity_indicator = "🔴" if severity == 'high' else "🟡" if severity == 'medium' else "🔵"
                        frequency_indicator = f" ({frequency}x)" if frequency > 1 else ""
                        specific_guidelines += f"- **Avoid {category} issues** {severity_indicator}: {description}{frequency_indicator}\n"
                        guidelines_added += 1
                        logger.info(f"  [+] Added issue guideline: {category} (freq: {frequency}, severity: {severity})")
                else:
                    logger.info(f"  [-] Skipped {issue_type}: {category} (freq: {frequency} < threshold: {frequency_threshold})")

            # Only add the section if we have guidelines to show
            if guidelines_added > 0:
                base_guidelines += specific_guidelines
                logger.info(f"Validation guidelines: Added {guidelines_added} specific improvements to prompt")
            else:
                # Add a note about learning in progress
                base_guidelines += f"\n### Learning in Progress:\n- Collecting validation feedback to improve future script generation\n- {len(common_issues)} feedback entries collected so far\n"
                logger.info(f"Validation guidelines: No guidelines met threshold, added learning progress note")
        else:
            logger.info("Validation guidelines: No common issues provided, using base guidelines only")

        final_guidelines_length = len(base_guidelines.strip())
        logger.info(f"Validation guidelines: Generated {final_guidelines_length} characters of guidelines")

        return base_guidelines.strip()

    except Exception as e:
        logger.error(f"Error generating validation guidelines: {e}")
        return "## Validation Guidelines\nFollow pytest and Selenium best practices."


def generate_enhanced_test_script_prompt(
    test_case: Dict[str, Any],
    step_matches: Dict[str, Any],
    test_data: Dict[str, Any],
    website_url: str,
    step_table_entry: Optional[Dict[str, Any]] = None,
    state: Optional[Any] = None,
    include_validation_feedback: bool = True,
    custom_instructions: Optional[str] = None
) -> str:
    """
    Generate an enhanced test script prompt with validation feedback integration using JSON step data.

    This function extends the JSON-based prompt generation with validation guidelines
    and feedback from previous script generations to improve quality.

    Args:
        test_case: The test case to generate a script for (used only for ID and metadata)
        step_matches: Dictionary of element matches for test steps
        test_data: Dictionary of test data values
        website_url: The URL of the website to test
        step_table_entry: The step table entry for the step (required for JSON-based approach)
        state: The application state manager instance (optional)
        include_validation_feedback: Whether to include validation feedback (default: True)
        custom_instructions: Optional custom instructions for script generation regeneration

    Returns:
        str: The enhanced prompt with validation guidelines
    """
    try:
        # Generate the base prompt using the JSON-based function
        base_prompt = generate_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            step_table_entry=step_table_entry,
            state=state
        )

        # If validation feedback is disabled or no state, return base prompt
        if not include_validation_feedback or not state:
            return base_prompt

        # Get common validation issues from state (simplified without deprecated analytics)
        common_issues = []  # Simplified - removed deprecated analytics method
        logger.info("Enhanced prompt: No common validation issues method available (analytics method removed)")

        # Generate validation guidelines with logging
        logger.info("Enhanced prompt: Generating validation guidelines with adaptive threshold")
        validation_guidelines = generate_validation_guidelines(common_issues)

        # Add custom instructions if provided
        custom_section = ""
        if custom_instructions:
            custom_section = f"""

**ADDITIONAL CUSTOM REQUIREMENTS:**
{custom_instructions}

Please ensure these custom requirements are addressed in addition to the standard guidelines above."""

        # Insert validation guidelines and custom instructions before the final instructions
        # Find the position to insert (before "Return the code.")
        insertion_point = base_prompt.rfind("Return the code.")

        if insertion_point != -1:
            # Insert validation guidelines and custom instructions before the final instruction
            enhanced_prompt = (
                base_prompt[:insertion_point] +
                validation_guidelines + custom_section + "\n\n        " +
                base_prompt[insertion_point:]
            )
        else:
            # Fallback: append validation guidelines and custom instructions
            enhanced_prompt = base_prompt + "\n\n" + validation_guidelines + custom_section

        # Add feedback context if available
        if common_issues:
            # Calculate feedback statistics
            total_issues = len([issue for issue in common_issues if issue.get('type') == 'issue'])
            total_recommendations = len([issue for issue in common_issues if issue.get('type') == 'recommendation'])
            high_severity_issues = len([issue for issue in common_issues if issue.get('severity') == 'high'])

            feedback_summary = f"\n\n## Recent Feedback Summary\n"
            feedback_summary += f"- **Validation History**: {len(common_issues)} feedback entries analyzed\n"
            feedback_summary += f"- **Issues Found**: {total_issues} unique issues ({high_severity_issues} high severity)\n"
            feedback_summary += f"- **Recommendations**: {total_recommendations} improvement suggestions\n"
            feedback_summary += f"- **Focus Areas**: Pay special attention to the specific improvements listed above\n"
            feedback_summary += f"- **Learning Status**: Feedback loop is actively improving script quality\n"

            enhanced_prompt += feedback_summary

        # Log custom instructions if provided
        if custom_instructions:
            logger.info(f"Enhanced prompt: Added custom instructions ({len(custom_instructions)} characters)")

        logger.info(f"Generated enhanced prompt with {len(common_issues)} validation guidelines")
        return enhanced_prompt

    except Exception as e:
        logger.error(f"Error generating enhanced test script prompt: {e}")
        # Fallback to base prompt on error
        return generate_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            step_table_entry=step_table_entry,
            state=state
        )
